<?php

namespace App\Http\Controllers;


use App\Jobs\DispatchFinishTask;
use App\Jobs\DispatchOpenCallback;
use App\Jobs\DispatchOrderNotify;
use App\Jobs\RefundO2oErrandOrder;
use App\Models\O2oErrandOrder;
use App\Models\Rider;
use App\Models\User;
use App\Services\Amap\GaodeService;
use App\Services\ChuanglanSmsApi;
use App\Services\CommonService;
use App\Services\EasemobService;
use App\Services\LocationService;
use App\Services\MapService;
use App\Services\RiderOrderService;
use Carbon\Carbon;
use Dcat\Admin\Form\Field\Map;
use Illuminate\Http\Request;
use Mitoop\JPush\JPushService;
use Predis\Client;
use JPush\Client as JPush;

class TestController extends Controller
{
    //
    public function test(Request $request,CommonService $service)
    {
        
        $orderId = $request->get("order_id");
        if (!$orderId) {
            dd('缺少订单ID');
        }
        $order = O2oErrandOrder::find($orderId);
        switch ($request->get("action")) {
            case 1: // 取消
                $this->dispatch(new RefundO2oErrandOrder($order->order_no, 1));
                break;
            case 2: // 接单
                $riderId = $request->get("rider_id");
                if (!$riderId) {
                    dd('缺少骑手ID');
                }
                $rider = Rider::query()->where('id', $riderId)->first();
                (new RiderOrderService)->pickupOrder($order, $rider);
                break;
            case 3: // 到达
                $order->order_status = O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT;
                $order->arrive_at = Carbon::now();
                $order->save();
                break;
            case 4: // 取到货
                $order->order_status = O2oErrandOrder::STATUS_DELIVERY;
                $order->pickup_at = Carbon::now();
                $order->save();
                break;
            case 5: // 完成
                $order->order_status = O2oErrandOrder::STATUS_FINISH;
                $order->finish_time = Carbon::now();
                $order->save();
                $this->dispatch(new DispatchFinishTask($order));
                break;
            default:
                $this->dispatch(new DispatchOpenCallback($order, 1));
        }
        dd(13);



//        $mapService = new MapService();
//        $mapService->geocoder('119.99', '30.27', 1);
        dd(123);
//        $res= app(CommonService::class)->getPointSite(['lng' => $request->lng, 'lat' => $request->lat]);
//        dd($res);
//        $point = ['lng'=>119.996752,'lat'=>30.275903];
//        $result = $service->pointInSite($point, 0);
//        dd($result);
//
//        $site = Site::with('regions')->find(1);

        $user = User::find(2);

        // 1020230207013850478875
        // 2023020722001459991431320186
        $eservice = new EasemobService('easemob_pt');
        $res = $eservice->sendMessageText([$user->easemob_user_id], $target_type = 'users', $message = "你好啊1111", $send_user = EasemobService::PT_SYSTEM_USER, $ext = ['msg_type' => 1]);
        dd($res);
//        $eservice->
//
//        $user = \Easemob::getUser(EasemobService::SQ_SYSTEM_USER);
//        dd($user);
//        $list = $eservice->getUserInfoList([EasemobService::SQ_SYSTEM_USER]);
//
//
//        $res= $eservice->editUserInfo(EasemobService::SQ_SYSTEM_USER, '系统信息', 'https://img.dingdongjuhe.com/images/logo.jpeg', '');


//        $res = $servicesendMessageText([$user->easemob_user_id], $target_type = 'users', $message = "你好啊1111", $send_user = EasemobService::SQ_SYSTEM_USER, $ext = []);

        // 119.996752,30.275903

    }

    private function redisGeo()
    {
        $redis = new Client();
        var_dump($redis->geoadd("user_2", 120, 30, "a")); //添加点位置
        var_dump($redis->geoadd("user_2", 120.1, 30.1, "b")); //添加点位置
        var_dump($redis->geopos("user_2", ["a", "b"])); //获取指定点位置
        var_dump($redis->geohash("user_2", ["a", "b"]));
        var_dump($redis->geodist("user_2", "a", "b")); // 获取两点的距离
        var_dump($redis->georadius("user_2", 120.2, 30.2, 200, "km", ["WITHDIST", "WITHCOORD", "COUNT 5", "ASC"]));
        var_dump($redis->geoadd("user_2", 120.2, 30.2, "c")); //添加点位置
        var_dump($redis->georadiusbymember("user_2", "c", 200, "km", ["WITHDIST", "WITHCOORD", "COUNT 5", "ASC"]));
    }
}
